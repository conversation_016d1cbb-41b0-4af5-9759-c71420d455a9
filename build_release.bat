@echo off
echo ========================================
echo    PRESS EXE OLUSTURUCU
echo ========================================
echo.

REM Visual Studio Build Tools'u ara
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if not exist "%VSWHERE%" (
    echo HATA: Visual Studio bulunamadi!
    echo Lutfen Visual Studio 2019 veya 2022 yukleyin.
    pause
    exit /b 1
)

REM Visual Studio yolunu bul
for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
    set "VS_PATH=%%i"
)

if not defined VS_PATH (
    echo HATA: Visual Studio C++ araclari bulunamadi!
    echo Lutfen Visual Studio ile C++ gelistirme araclarini yukleyin.
    pause
    exit /b 1
)

echo Visual Studio bulundu: %VS_PATH%
echo.

REM MSBuild yolunu ayarla
set "MSBUILD=%VS_PATH%\MSBuild\Current\Bin\MSBuild.exe"
if not exist "%MSBUILD%" (
    set "MSBUILD=%VS_PATH%\MSBuild\15.0\Bin\MSBuild.exe"
)

if not exist "%MSBUILD%" (
    echo HATA: MSBuild bulunamadi!
    pause
    exit /b 1
)

echo MSBuild bulundu: %MSBUILD%
echo.

REM Eski dosyalari temizle
echo Eski dosyalar temizleniyor...
if exist "x64\Release\press.exe" del "x64\Release\press.exe"
if exist "press_portable.exe" del "press_portable.exe"

REM Release modunda derle
echo.
echo ========================================
echo    DERLEME BASLIYOR...
echo ========================================
"%MSBUILD%" press.sln /p:Configuration=Release /p:Platform=x64 /p:UseEnv=true /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Derleme basarisiz!
    pause
    exit /b 1
)

REM Exe dosyasini kontrol et
if not exist "x64\Release\press.exe" (
    echo.
    echo HATA: press.exe olusturulamadi!
    pause
    exit /b 1
)

REM Portable exe olustur
echo.
echo Portable exe olusturuluyor...
copy "x64\Release\press.exe" "press_portable.exe" >nul

echo.
echo ========================================
echo    BASARILI!
echo ========================================
echo.
echo Olusturulan dosyalar:
echo   - x64\Release\press.exe
echo   - press_portable.exe (tasinabilir)
echo.
echo Bu exe dosyalarini baska bilgisayarlara kopyalayabilirsiniz.
echo Herhangi bir kurulum gerektirmez.
echo.
pause
