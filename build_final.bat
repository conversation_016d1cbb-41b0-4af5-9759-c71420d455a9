@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    PRESS EXE OLUSTURUCU (FINAL)
echo ========================================
echo.

REM Visual Studio yollarini dene
set "VS_PATHS=C:\Program Files\Microsoft Visual Studio\2022\Community;C:\Program Files (x86)\Microsoft Visual Studio\2019\Community;C:\Program Files\Microsoft Visual Studio\2022\Professional;C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional"

set "VCVARS_FOUND="
for %%p in (%VS_PATHS:;= %) do (
    if exist "%%p\VC\Auxiliary\Build\vcvars64.bat" (
        set "VCVARS_PATH=%%p\VC\Auxiliary\Build\vcvars64.bat"
        set "VCVARS_FOUND=1"
        echo Visual Studio bulundu: %%p
        goto :found_vs
    )
)

:found_vs
if not defined VCVARS_FOUND (
    echo HATA: Visual Studio bulunamadi!
    echo.
    echo Lutfen asagidaki linkten Visual Studio Community yukleyin:
    echo https://visualstudio.microsoft.com/downloads/
    echo.
    echo Kurulum sirasinda "C++ ile masaustu gelistirme" secenegini secin.
    echo.
    pause
    exit /b 1
)

echo.
echo Eski dosyalar temizleniyor...
if exist "press.exe" del "press.exe"
if exist "press.obj" del "press.obj"

echo.
echo ========================================
echo    DERLEME BASLIYOR...
echo ========================================
echo.

REM Gecici bat dosyasi olustur
echo @echo off > temp_build.bat
echo call "!VCVARS_PATH!" >> temp_build.bat
echo cl.exe /O2 /MT /EHsc press.cpp /Fe:press.exe /link /SUBSYSTEM:CONSOLE user32.lib >> temp_build.bat

REM Gecici bat dosyasini calistir
call temp_build.bat

REM Gecici dosyayi sil
del temp_build.bat

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Derleme basarisiz!
    echo.
    echo Olasi nedenler:
    echo 1. press.cpp dosyasinda syntax hatasi
    echo 2. Visual Studio C++ araclari eksik
    echo.
    pause
    exit /b 1
)

if not exist "press.exe" (
    echo.
    echo HATA: press.exe olusturulamadi!
    pause
    exit /b 1
)

REM Gereksiz dosyalari temizle
if exist "press.obj" del "press.obj"

REM Dosya boyutunu hesapla
for %%A in (press.exe) do set "FILE_SIZE=%%~zA"
set /a "FILE_SIZE_KB=!FILE_SIZE! / 1024"

echo.
echo ========================================
echo    BASARILI!
echo ========================================
echo.
echo Olusturulan dosya:
echo   - press.exe (!FILE_SIZE_KB! KB)
echo.
echo Bu exe dosyasini baska bilgisayarlara kopyalayabilirsiniz.
echo Herhangi bir kurulum gerektirmez.
echo.
echo Test etmek icin: press.exe
echo.
pause
