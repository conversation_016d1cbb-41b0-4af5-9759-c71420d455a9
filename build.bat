@echo off
echo Derleme baslaniyor...
if exist "C:\MinGW\bin\g++.exe" (
    C:\MinGW\bin\g++.exe -o standalone.exe standalone.cpp -static -O2
) else if exist "C:\TDM-GCC-64\bin\g++.exe" (
    C:\TDM-GCC-64\bin\g++.exe -o standalone.exe standalone.cpp -static -O2
) else (
    echo GCC derleyici bulunamadi!
    echo Lutfen MinGW veya TDM-GCC yukleyin
    pause
    exit /b 1
)
echo Derleme tama<PERSON>landi!
echo standalone.exe olusturuldu
pause
