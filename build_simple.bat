@echo off
echo ========================================
echo    BASIT EXE OLUSTURUCU
echo ========================================
echo.

REM Visual Studio Developer Command Prompt'u baslat
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if %ERRORLEVEL% neq 0 (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %ERRORLEVEL% neq 0 (
        echo HATA: Visual Studio Developer Command Prompt bulunamadi!
        echo Lutfen Visual Studio 2019 veya 2022 yukleyin.
        pause
        exit /b 1
    )
)

echo Visual Studio ortami yuklendi.
echo.

REM Eski dosyalari temizle
if exist "press.exe" del "press.exe"

echo ========================================
echo    DERLEME BASLIYOR...
echo ========================================
echo.

REM Dogrudan cl.exe ile derle (Windows API kutuphaneleri ile)
cl.exe /O2 /MT /EHsc press.cpp /Fe:press.exe /link /SUBSYSTEM:CONSOLE user32.lib

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Derleme basarisiz!
    pause
    exit /b 1
)

if not exist "press.exe" (
    echo.
    echo HATA: press.exe olusturulamadi!
    pause
    exit /b 1
)

REM Gereksiz dosyalari temizle
if exist "press.obj" del "press.obj"

echo.
echo ========================================
echo    BASARILI!
echo ========================================
echo.
echo Olusturulan dosya:
echo   - press.exe
echo.
echo Bu exe dosyasini baska bilgisayarlara kopyalayabilirsiniz.
echo Herhangi bir kurulum gerektirmez.
echo.
pause
