#include <windows.h>
#include <cstdio>
#include <cstring>

// Optimized key press functions
inline void pressKey(BYTE key, DWORD hold_ms = 50) {
    keybd_event(key, 0, 0, 0);
    Sleep(hold_ms);
    keybd_event(key, 0, KEYEVENTF_KEYUP, 0);
}

inline void pressCombo(BYTE key1, BYTE key2, DWORD hold_ms = 500) {
    keybd_event(key1, 0, 0, 0);
    keybd_event(key2, 0, 0, 0);
    Sleep(hold_ms);
    keybd_event(key2, 0, KEYEVENTF_KEYUP, 0);
    keybd_event(key1, 0, KEYEVENTF_KEYUP, 0);
}

// Optimized console output
void writeConsole(const char* text) {
    static HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD written;
    WriteConsoleA(hConsole, text, (DWORD)strlen(text), &written, NULL);
}

void writeConsoleF(const char* format, int value) {
    char buffer[64];
    sprintf_s(buffer, sizeof(buffer), format, value);
    writeConsole(buffer);
}

// Optimized key state checking
bool checkToggleKey() {
    static bool lastState = false;
    bool currentState = (GetAsyncKeyState('X') & 0x8000) != 0;
    
    if (currentState && !lastState) {
        // Wait for key release to prevent multiple triggers
        while (GetAsyncKeyState('X') & 0x8000) {
            Sleep(10);
        }
        lastState = false;
        return true;
    }
    
    lastState = currentState;
    return false;
}

// Random number generator (simple and fast)
class SimpleRandom {
private:
    unsigned int seed;
public:
    SimpleRandom() : seed((unsigned int)GetTickCount()) {}
    
    int range(int min, int max) {
        seed = seed * 1103515245 + 12345;
        return min + (seed % (max - min + 1));
    }
};

int main() {
    // Set console title
    SetConsoleTitleA("Press Bot - X ile Baslat/Durdur");
    
    // Initialize
    writeConsole("========================================\n");
    writeConsole("           PRESS BOT v2.0\n");
    writeConsole("========================================\n");
    writeConsole("X tusuna basin: Dongu baslasin/dursun\n");
    writeConsole("Cikmak icin: Ctrl+C\n");
    writeConsole("========================================\n\n");
    
    SimpleRandom rng;
    bool isRunning = false;
    
    while (true) {
        // Check for toggle
        if (checkToggleKey()) {
            isRunning = !isRunning;
            if (isRunning) {
                writeConsole("\n>>> BASLATILDI <<<\n");
            } else {
                writeConsole("\n>>> DURDU <<<\n");
            }
        }
        
        if (isRunning) {
            // Phase 1: W+A combo 20 times
            writeConsole("\n[FAZE 1] W+A kombinasyonu (20 kez)...\n");
            for (int i = 0; i < 20 && isRunning; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    writeConsole("\n>>> DURDU <<<\n");
                    break;
                }
                
                pressCombo('W', 'A', 500);
                writeConsoleF("W+A %d/20 tamamlandi\n", i + 1);
                
                // Random wait between 2-2.1 seconds
                if (i < 19) {
                    int wait_time = rng.range(2000, 2100);
                    for (int j = 0; j < wait_time / 50 && isRunning; j++) {
                        if (checkToggleKey()) {
                            isRunning = false;
                            writeConsole("\n>>> DURDU <<<\n");
                            break;
                        }
                        Sleep(50);
                    }
                }
            }
            
            if (!isRunning) continue;
            
            // Phase 2: S+D hold for 9 seconds
            writeConsole("\n[FAZE 2] S+D tuslarini 9 saniye basili tutma...\n");
            keybd_event('S', 0, 0, 0);
            keybd_event('D', 0, 0, 0);
            
            for (int i = 0; i < 90 && isRunning; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    break;
                }
                Sleep(100);
            }
            
            keybd_event('D', 0, KEYEVENTF_KEYUP, 0);
            keybd_event('S', 0, KEYEVENTF_KEYUP, 0);
            
            if (!isRunning) {
                writeConsole("\n>>> DURDU <<<\n");
                continue;
            }
            
            writeConsole("S+D islemi tamamlandi\n");
            
            // Phase 3: Extra S press for 2 seconds
            writeConsole("\n[FAZE 3] Ekstra S tusu (2 saniye)...\n");
            keybd_event('S', 0, 0, 0);
            
            for (int i = 0; i < 20 && isRunning; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    break;
                }
                Sleep(100);
            }
            
            keybd_event('S', 0, KEYEVENTF_KEYUP, 0);
            
            if (!isRunning) {
                writeConsole("\n>>> DURDU <<<\n");
                continue;
            }
            
            // Phase 4: Wait 10 seconds
            writeConsole("\n[FAZE 4] 10 saniye bekleme...\n");
            for (int i = 0; i < 100 && isRunning; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    break;
                }
                Sleep(100);
            }
            
            if (isRunning) {
                writeConsole("\n>>> DONGU TAMAMLANDI - TEKRAR BASLIYOR <<<\n");
            } else {
                writeConsole("\n>>> DURDU <<<\n");
            }
        }
        
        Sleep(10); // Reduced CPU usage
    }
    
    return 0;
}
