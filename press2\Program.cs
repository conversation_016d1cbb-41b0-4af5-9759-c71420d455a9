﻿using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;

namespace PressBot
{
    /// <summary>
    /// Press Bot - Klavye Otomasyonu Uygulaması
    /// X tuşu ile başlatır/durdurur, Ctrl+C ile çıkar
    /// </summary>
    class Program
    {
        // Windows API fonksiyonları
        [DllImport("user32.dll")]
        static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, UIntPtr dwExtraInfo);

        [DllImport("user32.dll")]
        static extern short GetAsyncKeyState(int vKey);

        [DllImport("kernel32.dll")]
        static extern void Sleep(uint dwMilliseconds);

        // Sabitler
        const int KEYEVENTF_KEYUP = 0x0002;
        const int VK_X = 0x58;
        const int VK_W = 0x57;
        const int VK_A = 0x41;
        const int VK_S = 0x53;
        const int VK_D = 0x44;

        // Global değişkenler
        private static bool isRunning = false;
        private static bool programActive = true;
        private static readonly Random random = new Random();
        private static bool lastXState = false;

        static async Task Main(string[] args)
        {
            // Console ayarları
            Console.Title = "Press Bot v2.0 - C# Edition";
            Console.CancelKeyPress += (sender, e) => {
                Console.WriteLine("\n\n>>> PROGRAM SONLANDIRILIYOR <<<");
                programActive = false;
                e.Cancel = true;
            };

            // Başlangıç mesajları
            ShowWelcomeMessage();

            // Ana döngü
            await RunMainLoop();

            Console.WriteLine("\nProgram sonlandırıldı. İyi günler!");
        }

        static void ShowWelcomeMessage()
        {
            Console.Clear();
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("========================================");
            Console.WriteLine("         PRESS BOT v2.0");
            Console.WriteLine("       C# Edition - 2024");
            Console.WriteLine("========================================");
            Console.ResetColor();
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("KONTROLLER:");
            Console.WriteLine("  X tuşu     : Otomasyonu Başlat/Durdur");
            Console.WriteLine("  Ctrl+C     : Programdan Çık");
            Console.ResetColor();
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("OTOMASYON DÖNGÜSÜ:");
            Console.WriteLine("  1. W+A kombinasyonu (20 kez, 2-2.1s aralık)");
            Console.WriteLine("  2. S+D tuşları (9 saniye basılı)");
            Console.WriteLine("  3. S tuşu (2 saniye ekstra)");
            Console.WriteLine("  4. Bekleme (10 saniye)");
            Console.ResetColor();
            Console.WriteLine();
            Console.WriteLine("========================================");
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.White;
            Console.WriteLine("Hazır! X tuşuna basarak başlayabilirsiniz...");
            Console.ResetColor();
            Console.WriteLine();
        }

        static async Task RunMainLoop()
        {
            while (programActive)
            {
                // X tuşu kontrolü
                if (CheckToggleKey())
                {
                    isRunning = !isRunning;
                    if (isRunning)
                    {
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] >>> OTOMASYON BAŞLATILDI <<<");
                        Console.ResetColor();
                    }
                    else
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] >>> OTOMASYON DURDURULDU <<<");
                        Console.ResetColor();
                    }
                }

                // Otomasyon döngüsü
                if (isRunning)
                {
                    await RunAutomationCycle();
                }

                await Task.Delay(50); // CPU kullanımını azalt
            }
        }

        static bool CheckToggleKey()
        {
            bool currentXState = (GetAsyncKeyState(VK_X) & 0x8000) != 0;

            if (currentXState && !lastXState)
            {
                // Tuş bırakılana kadar bekle
                while ((GetAsyncKeyState(VK_X) & 0x8000) != 0)
                {
                    Thread.Sleep(10);
                }
                lastXState = false;
                return true;
            }

            lastXState = currentXState;
            return false;
        }

        static async Task RunAutomationCycle()
        {
            try
            {
                // Faz 1: W+A kombinasyonu
                Console.ForegroundColor = ConsoleColor.Cyan;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] [FAZ 1] W+A kombinasyonu başlıyor...");
                Console.ResetColor();

                for (int i = 0; i < 20 && isRunning && programActive; i++)
                {
                    if (CheckToggleKey())
                    {
                        isRunning = false;
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] >>> DURDURULDU <<<");
                        Console.ResetColor();
                        return;
                    }

                    PressCombo(VK_W, VK_A, 500);
                    Console.WriteLine($"  W+A {i + 1}/20 tamamlandı");

                    if (i < 19)
                    {
                        int waitTime = random.Next(2000, 2101);
                        await DelayWithCancellation(waitTime);
                        if (!isRunning) return;
                    }
                }

                if (!isRunning) return;

                // Faz 2: S+D basılı tutma
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] [FAZ 2] S+D tuşları 9 saniye basılı tutuluyor...");
                Console.ResetColor();

                keybd_event(VK_S, 0, 0, UIntPtr.Zero);
                keybd_event(VK_D, 0, 0, UIntPtr.Zero);

                await DelayWithCancellation(9000);

                keybd_event(VK_D, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
                keybd_event(VK_S, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);

                if (!isRunning)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] >>> DURDURULDU <<<");
                    Console.ResetColor();
                    return;
                }

                Console.WriteLine("  S+D işlemi tamamlandı");

                // Faz 3: Ekstra S tuşu
                Console.ForegroundColor = ConsoleColor.Magenta;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] [FAZ 3] Ekstra S tuşu (2 saniye)...");
                Console.ResetColor();

                keybd_event(VK_S, 0, 0, UIntPtr.Zero);
                await DelayWithCancellation(2000);
                keybd_event(VK_S, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);

                if (!isRunning)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] >>> DURDURULDU <<<");
                    Console.ResetColor();
                    return;
                }

                Console.WriteLine("  Ekstra S işlemi tamamlandı");

                // Faz 4: Bekleme
                Console.ForegroundColor = ConsoleColor.Gray;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] [FAZ 4] 10 saniye bekleniyor...");
                Console.ResetColor();

                await DelayWithCancellation(10000);

                if (isRunning)
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] >>> DÖNGÜ TAMAMLANDI - TEKRAR BAŞLIYOR <<<");
                    Console.ResetColor();
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"HATA: {ex.Message}");
                Console.ResetColor();
                isRunning = false;
            }
        }

        static void PressKey(byte key, uint holdMs = 50)
        {
            keybd_event(key, 0, 0, UIntPtr.Zero);
            Sleep(holdMs);
            keybd_event(key, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        }

        static void PressCombo(byte key1, byte key2, uint holdMs = 500)
        {
            keybd_event(key1, 0, 0, UIntPtr.Zero);
            keybd_event(key2, 0, 0, UIntPtr.Zero);
            Sleep(holdMs);
            keybd_event(key2, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
            keybd_event(key1, 0, KEYEVENTF_KEYUP, UIntPtr.Zero);
        }

        static async Task DelayWithCancellation(int milliseconds)
        {
            int elapsed = 0;
            const int checkInterval = 100;

            while (elapsed < milliseconds && isRunning && programActive)
            {
                if (CheckToggleKey())
                {
                    isRunning = false;
                    return;
                }

                int delayTime = Math.Min(checkInterval, milliseconds - elapsed);
                await Task.Delay(delayTime);
                elapsed += delayTime;
            }
        }
    }
}
