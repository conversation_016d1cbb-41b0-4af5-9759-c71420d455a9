# Press Bot v2.0 - C# Edition

Modern C# ile yazılmış klavye otomasyonu uygulaması. Oyunlarda ve tekrarlayan görevlerde kullanılabilir.

## 🚀 Hızlı Başlangıç

### Hazır Exe Kullanımı (ÖNERİLEN)
```bash
press_bot.exe
```
Bu exe dosyası tamamen bağımsızdır, .NET runtime gerektirmez.

### <PERSON><PERSON><PERSON>
```bash
# Release build (optimize edilmiş)
.\build_release.bat

# Debug build (test için)
.\build_debug.bat

# Manuel derleme
dotnet publish --configuration Release --runtime win-x64 --self-contained true
```

## 📋 Gereksinimler

### Hazır Exe İçin:
- Windows 10/11 (x64)
- Her<PERSON><PERSON> bir kurulum gerektirmez

### Kaynak Koddan Derleme İçin:
- .NET 8.0 SDK veya üzeri
- Windows 10/11

## 🎮 Kullanım

1. `press_bot.exe` dosyasını çalıştırın
2. **X tuşu**: Otomasyonu başlatır/durdurur
3. **Ctrl+C**: Programdan çıkar

## 🔧 Program Davranışı

Program çalıştığında şu döngüyü gerçekleştirir:

### Faz 1: W+A Kombinasyonu
- 20 kez W+A tuş kombinasyonu
- Her kombinasyon arası 2-2.1 saniye rastgele bekleme
- Gerçek zamanlı ilerleme göstergesi

### Faz 2: S+D Basılı Tutma
- S ve D tuşlarını 9 saniye basılı tutar
- Süre boyunca X tuşu ile durdurulabilir

### Faz 3: Ekstra S Tuşu
- S tuşunu 2 saniye ekstra basılı tutar
- Hassas zamanlama kontrolü

### Faz 4: Bekleme Periyodu
- 10 saniye bekleme
- Döngü otomatik olarak tekrarlanır

## 🎨 Özellikler

### Gelişmiş Kullanıcı Arayüzü
- Renkli konsol çıktısı
- Gerçek zamanlı durum göstergeleri
- Zaman damgalı log mesajları
- Faz bazlı ilerleme takibi

### Performans Optimizasyonları
- Asenkron programlama (async/await)
- Düşük CPU kullanımı
- Hassas zamanlama kontrolü
- Bellek optimizasyonu

### Güvenlik ve Kontrol
- Anlık durdurma (X tuşu)
- Güvenli çıkış (Ctrl+C)
- Exception handling
- Kaynak temizleme

## 📁 Dosya Yapısı

```
press2/
├── Program.cs              # Ana kaynak kod
├── press2.csproj          # Proje dosyası
├── press2.sln             # Solution dosyası
├── build_release.bat      # Release build scripti
├── build_debug.bat        # Debug build scripti
├── README.md              # Bu dosya
├── press_bot.exe          # Derlenmiş program (12MB)
└── publish/               # Publish çıktıları
    └── press2.exe         # Self-contained exe
```

## 🛠️ Teknik Detaylar

### Kullanılan Teknolojiler
- **.NET 8.0**: Modern C# framework
- **Windows API**: Klavye simülasyonu (user32.dll)
- **Async/Await**: Asenkron programlama
- **Self-Contained**: Bağımsız exe

### Derleme Optimizasyonları
- **PublishSingleFile**: Tek dosya exe
- **SelfContained**: .NET runtime dahil
- **PublishTrimmed**: Kullanılmayan kod temizleme
- **TrimMode=link**: Agresif boyut optimizasyonu

### Windows API Fonksiyonları
```csharp
keybd_event()     // Klavye tuş simülasyonu
GetAsyncKeyState() // Tuş durumu kontrolü
Sleep()           // Hassas zamanlama
```

## 🔒 Güvenlik

- Program sadece klavye tuşlarını simüle eder
- Ağ bağlantısı kullanmaz
- Dosya sistemi erişimi yapmaz
- Tamamen yerel çalışır
- Açık kaynak kod

## 🚨 Uyarılar

- Bu program oyunlarda kullanılırsa, oyun kurallarını ihlal edebilir
- Kullanım sorumluluğu kullanıcıya aittir
- Anti-cheat sistemler tarafından tespit edilebilir
- Sadece eğitim ve test amaçlı kullanın

## 🔧 Sorun Giderme

### "Program çalışmıyor"
1. Windows 10/11 kullandığınızdan emin olun
2. Antivirus programını kontrol edin
3. Yönetici yetkisiyle çalıştırın

### "Tuşlar basılmıyor"
1. Program aktif pencerede çalışır
2. Hedef uygulamanın odakta olduğundan emin olun
3. Yönetici yetkisi gerekebilir

### "Derleme hatası"
1. .NET 8.0 SDK kurulu olduğundan emin olun
2. `dotnet --version` ile kontrol edin
3. Visual Studio Code veya Visual Studio kullanın

## 📊 Performans

- **Exe Boyutu**: ~12MB (self-contained)
- **RAM Kullanımı**: ~15-20MB
- **CPU Kullanımı**: %0.1-0.5
- **Başlangıç Süresi**: <1 saniye

## 📞 Destek

Sorun yaşıyorsanız:
1. README.md dosyasını okuyun
2. Hata mesajlarını kontrol edin
3. .NET SDK kurulumunu kontrol edin

## 📄 Lisans

Bu proje eğitim amaçlıdır. Kullanım sorumluluğu kullanıcıya aittir.

---

**Press Bot v2.0** - Modern C# ile geliştirilmiş klavye otomasyonu çözümü.
