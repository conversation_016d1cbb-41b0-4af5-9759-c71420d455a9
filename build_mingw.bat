@echo off
echo ========================================
echo    MINGW ILE EXE OLUSTURUCU
echo ========================================
echo.

REM MinGW yollarini kontrol et
set "MINGW_PATHS=C:\MinGW\bin;C:\TDM-GCC-64\bin;C:\msys64\mingw64\bin;C:\mingw64\bin"

set "GCC_FOUND="
for %%p in (%MINGW_PATHS:;= %) do (
    if exist "%%p\g++.exe" (
        set "GCC_PATH=%%p\g++.exe"
        set "GCC_FOUND=1"
        goto :found_gcc
    )
)

:found_gcc
if not defined GCC_FOUND (
    echo HATA: MinGW/GCC bulunamadi!
    echo.
    echo Lutfen asagidaki yollardan birinde MinGW yukleyin:
    echo   - C:\MinGW\bin\
    echo   - C:\TDM-GCC-64\bin\
    echo   - C:\msys64\mingw64\bin\
    echo.
    echo Indirme linkleri:
    echo   - MinGW: https://sourceforge.net/projects/mingw/
    echo   - TDM-GCC: https://jmeubank.github.io/tdm-gcc/
    echo   - MSYS2: https://www.msys2.org/
    echo.
    pause
    exit /b 1
)

echo GCC bulundu: %GCC_PATH%
echo.

REM Eski dosyalari temizle
echo Eski dosyalar temizleniyor...
if exist "press_mingw.exe" del "press_mingw.exe"

REM Derleme parametreleri
set "COMPILE_FLAGS=-std=c++17 -O3 -s -static -static-libgcc -static-libstdc++"
set "LINK_FLAGS=-Wl,--gc-sections -Wl,--strip-all"

echo.
echo ========================================
echo    DERLEME BASLIYOR...
echo ========================================
echo.
echo Kullanilan parametreler:
echo   Optimizasyon: -O3 (maksimum)
echo   Static linking: -static
echo   Strip symbols: -s
echo   Garbage collection: --gc-sections
echo.

"%GCC_PATH%" %COMPILE_FLAGS% %LINK_FLAGS% -o press_mingw.exe press.cpp

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Derleme basarisiz!
    echo.
    echo Olasi cozumler:
    echo   1. press.cpp dosyasinda syntax hatasi var
    echo   2. MinGW versiyonu eski olabilir
    echo   3. Windows.h kutuphanesi bulunamiyor
    echo.
    pause
    exit /b 1
)

REM Exe dosyasini kontrol et
if not exist "press_mingw.exe" (
    echo.
    echo HATA: press_mingw.exe olusturulamadi!
    pause
    exit /b 1
)

REM Dosya boyutunu goster
for %%A in (press_mingw.exe) do set "FILE_SIZE=%%~zA"
set /a "FILE_SIZE_KB=%FILE_SIZE% / 1024"

echo.
echo ========================================
echo    BASARILI!
echo ========================================
echo.
echo Olusturulan dosya:
echo   - press_mingw.exe (%FILE_SIZE_KB% KB)
echo.
echo Bu exe dosyasi tamamen bagimsizdir.
echo Herhangi bir bilgisayarda calisir.
echo Kurulum gerektirmez.
echo.
pause
