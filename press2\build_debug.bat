@echo off
echo ========================================
echo    PRESS BOT C# - DEBUG BUILD
echo ========================================
echo.

REM .NET SDK kontrolu
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo HATA: .NET SDK bulunamadi!
    echo.
    echo Lutfen .NET 8.0 SDK yukleyin:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo .NET SDK bulundu:
dotnet --version
echo.

echo ========================================
echo    DEBUG BUILD BASLIYOR...
echo ========================================
echo.

REM Debug build ve calistir
dotnet run --configuration Debug

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Build veya calistirma basarisiz!
    pause
    exit /b 1
)

echo.
echo Program sonlandi.
pause
