@echo off
echo ========================================
echo    PRESS EXE OLUSTURUCU
echo ========================================
echo.

REM Visual Studio Build Tools'u ara
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if not exist "%VSWHERE%" (
    echo Visual Studio bulunamadi, alternatif yontem deneniyor...
    goto :try_msbuild
)

REM Visual Studio yolunu bul
for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
    set "VS_PATH=%%i"
)

if defined VS_PATH (
    echo Visual Studio bulundu: %VS_PATH%
    set "MSBUILD=%VS_PATH%\MSBuild\Current\Bin\MSBuild.exe"
    if not exist "%MSBUILD%" (
        set "MSBUILD=%VS_PATH%\MSBuild\15.0\Bin\MSBuild.exe"
    )
    goto :build
)

:try_msbuild
REM Sistem PATH'inde MSBuild ara
where msbuild >nul 2>&1
if %ERRORLEVEL% equ 0 (
    set "MSBUILD=msbuild"
    echo MSBuild sistem PATH'inde bulundu
    goto :build
)

REM Manuel MSBuild yollari
set "MSBUILD_PATHS=%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe;%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe;%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe;%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"

for %%p in (%MSBUILD_PATHS:;= %) do (
    if exist "%%p" (
        set "MSBUILD=%%p"
        echo MSBuild bulundu: %%p
        goto :build
    )
)

echo HATA: MSBuild bulunamadi!
echo Lutfen Visual Studio 2019 veya 2022 yukleyin.
pause
exit /b 1

:build
echo.
echo Eski dosyalar temizleniyor...
if exist "x64\Release\press.exe" del "x64\Release\press.exe"
if exist "press_portable.exe" del "press_portable.exe"

echo.
echo ========================================
echo    DERLEME BASLIYOR...
echo ========================================

"%MSBUILD%" press.sln /p:Configuration=Release /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Derleme basarisiz!
    pause
    exit /b 1
)

if not exist "x64\Release\press.exe" (
    echo.
    echo HATA: press.exe olusturulamadi!
    pause
    exit /b 1
)

echo.
echo Portable exe olusturuluyor...
copy "x64\Release\press.exe" "press_portable.exe" >nul

echo.
echo ========================================
echo    BASARILI!
echo ========================================
echo.
echo Olusturulan dosyalar:
echo   - x64\Release\press.exe
echo   - press_portable.exe (tasinabilir)
echo.
echo Bu exe dosyalarini baska bilgisayarlara kopyalayabilirsiniz.
echo.
pause
