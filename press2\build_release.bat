@echo off
echo ========================================
echo    PRESS BOT C# - RELEASE BUILD
echo ========================================
echo.

REM .NET SDK kontrolu
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo HATA: .NET SDK bulunamadi!
    echo.
    echo Lutfen .NET 8.0 SDK yukleyin:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

echo .NET SDK bulundu:
dotnet --version
echo.

REM Eski dosyalari temizle
echo Eski dosyalar temizleniyor...
if exist "bin\Release" rmdir /s /q "bin\Release"
if exist "obj\Release" rmdir /s /q "obj\Release"
if exist "press_bot.exe" del "press_bot.exe"

echo.
echo ========================================
echo    RELEASE BUILD BASLIYOR...
echo ========================================
echo.

REM Release build
dotnet build --configuration Release --verbosity minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Build basarisiz!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    PUBLISH BASLIYOR...
echo ========================================
echo.

REM Self-contained executable olustur
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output publish --verbosity minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo HATA: Publish basarisiz!
    pause
    exit /b 1
)

REM Ana exe dosyasini kopyala
if exist "publish\press2.exe" (
    copy "publish\press2.exe" "press_bot.exe" >nul
    echo.
    echo Portable exe olusturuldu: press_bot.exe
) else (
    echo.
    echo HATA: press2.exe bulunamadi!
    pause
    exit /b 1
)

REM Dosya boyutunu hesapla
for %%A in (press_bot.exe) do set "FILE_SIZE=%%~zA"
set /a "FILE_SIZE_MB=%FILE_SIZE% / 1048576"
set /a "FILE_SIZE_KB=(%FILE_SIZE% - %FILE_SIZE_MB% * 1048576) / 1024"

echo.
echo ========================================
echo    BUILD TAMAMLANDI!
echo ========================================
echo.
echo Olusturulan dosyalar:
echo   - press_bot.exe (%FILE_SIZE_MB%.%FILE_SIZE_KB% MB)
echo   - publish\ klasoru (tam kurulum)
echo.
echo press_bot.exe dosyasi:
echo   - Tamamen bagimsizdir
echo   - .NET runtime gerektirmez
echo   - Baska bilgisayarlara kopyalanabilir
echo.
echo Test etmek icin: press_bot.exe
echo.
pause
