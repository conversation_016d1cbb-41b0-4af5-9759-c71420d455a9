{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"press2/1.0.0": {"dependencies": {"Microsoft.NET.ILLink.Tasks": "8.0.13", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.13"}, "runtime": {"press2.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.13": {"runtime": {"System.Collections.Immutable.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}, "System.Console.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}, "System.IO.Compression.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}, "System.Private.CoreLib.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.1325.6609"}}}, "Microsoft.NET.ILLink.Tasks/8.0.13": {}}}, "libraries": {"press2/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.13": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.NET.ILLink.Tasks/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-R19ZTaRiQAK+xo9ZwaHbF/1vb1wwR1Wn5+sqp9v8+CDjbdS8R6qftKdw0VSXWKm7VAMi7P+NCU4zxDzhEWcAwQ==", "path": "microsoft.net.illink.tasks/8.0.13", "hashPath": "microsoft.net.illink.tasks.8.0.13.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}