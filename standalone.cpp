#include <windows.h>

int main() {
    bool isRunning = false;
    
    // Print instructions
    const char* instructions = "X tusuna basin, sonsuz dongu baslasin/dursun.\nCikmak icin Ctrl+C\n\n";
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD written;
    WriteConsoleA(hConsole, instructions, strlen(instructions), &written, NULL);

    while (true) {
        // Check X key for toggle
        if (GetAsyncKeyState('X') & 0x8000) {
            while (GetAsyncKeyState('X') & 0x8000) Sleep(50); // Wait for key release
            isRunning = !isRunning;
            const char* status = isRunning ? "\nBaslatildi...\n" : "\nDurdu...\n";
            WriteConsoleA(hConsole, status, strlen(status), &written, NULL);
        }

        if (isRunning) {
            for (int i = 0; i < 20 && isRunning; i++) {
                // Press W+A
                keybd_event('W', 0, 0, 0);
                keybd_event('A', 0, 0, 0);
                Sleep(500);
                keybd_event('A', 0, KEYEVENTF_KEYUP, 0);
                keybd_event('W', 0, KEYEVENTF_KEYUP, 0);

                char buffer[32];
                sprintf_s(buffer, "W+A %d/20\n", i + 1);
                WriteConsoleA(hConsole, buffer, strlen(buffer), &written, NULL);

                // Check for toggle during wait
                if (i < 19) {
                    for (int j = 0; j < 20 && isRunning; j++) {
                        if (GetAsyncKeyState('X') & 0x8000) {
                            while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                            isRunning = false;
                            WriteConsoleA(hConsole, "\nDurdu...\n", 10, &written, NULL);
                        }
                        Sleep(100);
                    }
                }
            }

            if (isRunning) {
                // S+D press for 9 seconds
                const char* msg = "\nS+D tusuna 9 saniye basiliyor...\n";
                WriteConsoleA(hConsole, msg, strlen(msg), &written, NULL);
                
                keybd_event('S', 0, 0, 0);
                keybd_event('D', 0, 0, 0);
                
                for (int i = 0; i < 90 && isRunning; i++) {
                    if (GetAsyncKeyState('X') & 0x8000) {
                        while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                        isRunning = false;
                    }
                    Sleep(100);
                }
                
                keybd_event('D', 0, KEYEVENTF_KEYUP, 0);
                keybd_event('S', 0, KEYEVENTF_KEYUP, 0);
                
                if (isRunning) {
                    const char* complete = "S+D islemi tamamlandi.\n";
                    WriteConsoleA(hConsole, complete, strlen(complete), &written, NULL);
                }
            }

            if (isRunning) {
                // Extra 2 seconds S press
                const char* msg = "Ekstra olarak 2 saniye S tusuna basiliyor...\n";
                WriteConsoleA(hConsole, msg, strlen(msg), &written, NULL);
                
                keybd_event('S', 0, 0, 0);
                for (int i = 0; i < 20 && isRunning; i++) {
                    if (GetAsyncKeyState('X') & 0x8000) {
                        while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                        isRunning = false;
                    }
                    Sleep(100);
                }
                keybd_event('S', 0, KEYEVENTF_KEYUP, 0);
            }

            if (isRunning) {
                // Wait 10 seconds
                const char* msg = "10 saniye bekleniyor...\n";
                WriteConsoleA(hConsole, msg, strlen(msg), &written, NULL);
                
                for (int i = 0; i < 100 && isRunning; i++) {
                    if (GetAsyncKeyState('X') & 0x8000) {
                        while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                        isRunning = false;
                    }
                    Sleep(100);
                }

                if (isRunning) {
                    const char* msg = "\nDongu tamamlandi, tekrar basliyor...\n";
                    WriteConsoleA(hConsole, msg, strlen(msg), &written, NULL);
                }
            }
        }
        Sleep(50);
    }
    return 0;
}
