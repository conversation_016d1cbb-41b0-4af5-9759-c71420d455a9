#include <iostream>
#include <windows.h>
#include <random>
#include <thread>

void pressKey(char key, int hold_ms = 50) {
    keybd_event(key, 0, 0, 0);
    Sleep(hold_ms);
    keybd_event(key, 0, KEYEVENTF_KEYUP, 0);
}

void pressCombo(char key1, char key2, int hold_ms = 50) {
    keybd_event(key1, 0, 0, 0);
    keybd_event(key2, 0, 0, 0);
    Sleep(hold_ms);
    keybd_event(key2, 0, KEYEVENTF_KEYUP, 0);
    keybd_event(key1, 0, KEYEVENTF_KEYUP, 0);
}

bool checkToggleKey() {
    static bool lastState = false;
    bool currentState = (GetAsyncKeyState('X') & 0x8000);
    if (currentState && !lastState) {
        lastState = true;
        return true;
    }
    else if (!currentState) {
        lastState = false;
    }
    return false;
}

int main() {
    std::cout << "X tusuna basin, sonsuz dongu baslasin/dursun.\n";
    std::cout << "Cikmak icin Ctrl+C\n\n";

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(2000, 2100);

    bool isRunning = false;

    while (true) {
        if (checkToggleKey()) {
            isRunning = !isRunning;
            if (isRunning)
                std::cout << "\nBaslatildi...\n";
            else
                std::cout << "\nDurdu...\n";
        }

        while (isRunning) {
            for (int i = 0; i < 20; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    std::cout << "\nDurdu...\n";
                    break;
                }

                pressCombo('W', 'A', 500);
                std::cout << "W+A " << i + 1 << "/20\n";

                if (i < 19) {
                    int wait_time = dis(gen);
                    for (int j = 0; j < wait_time / 100; j++) {
                        if (checkToggleKey()) {
                            isRunning = false;
                            std::cout << "\nDurdu...\n";
                            break;
                        }
                        Sleep(100);
                    }
                    if (!isRunning) break;
                }
            }

            if (!isRunning) break;

            std::cout << "\nS+D tusuna 9 saniye basiliyor...\n";
            keybd_event('S', 0, 0, 0);
            keybd_event('D', 0, 0, 0);
            for (int i = 0; i < 90; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    break;
                }
                Sleep(100);
            }
            keybd_event('D', 0, KEYEVENTF_KEYUP, 0);
            keybd_event('S', 0, KEYEVENTF_KEYUP, 0);
            std::cout << "S+D islemi tamamlandi.\n";

            if (!isRunning) break;

            // Eklenen kısım: 2 saniye S bas
            std::cout << "Ekstra olarak 2 saniye S tusuna basiliyor...\n";
            keybd_event('S', 0, 0, 0);
            for (int i = 0; i < 20; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    break;
                }
                Sleep(100);
            }
            keybd_event('S', 0, KEYEVENTF_KEYUP, 0);

            if (!isRunning) break;

            std::cout << "10 saniye bekleniyor...\n";
            for (int i = 0; i < 100; i++) {
                if (checkToggleKey()) {
                    isRunning = false;
                    break;
                }
                Sleep(100);
            }

            if (isRunning)
                std::cout << "\nDongu tamamlandi, tekrar basliyor...\n";
        }

        Sleep(50);
    }

    return 0;
}
